# BusinessLM Orchestration PoC — Migration Plan & Architecture

## 🗂️ File Migration Strategy

### 🟢 Files to Migrate and Polish

#### **Database & RAG Components** ✅ *COMPLETED*

* ✅ `backend/rag/` — **RAG system simplified and completed**
  * Removed code duplication (eliminated ~560 lines of duplicate code)
  * Completed missing implementations in vector_store.py and knowledge_base.py
  * Fixed all broken imports and circular dependencies
  ️ * Streamlined embeddings with factory pattern and fallback support
  * Consolidated retriever components (removed duplicate ContextWindowManager/QueryRewriter)
* `backend/app/db/postgres.py` — PostgreSQL connection *(clean up)*
* `backend/app/llm/adapters/` — LLM adapters *(clean interfaces)*

#### **Configuration & Utilities**

* `backend/app/core/config.py` — Configuration management
* `backend/app/core/errors.py` — Error handling
* Mock documents from `backend/data/mock_documents/`

### 🔴 Files to Rebuild from Scratch

#### **Orchestration System**

* All LangGraph components *(current implementation is too convoluted)*
* Agent implementations *(too much inheritance and complexity)*
* State management *(rebuild with orchestration-first design)*
* Tool system *(current is department-bound, need dynamic selection)*

#### **CLI & Testing**

* CLI tools *(current is overly complex)*
* Testing infrastructure *(rebuild for orchestration tracing)*

---

## 📁 New Repository Structure

```
businesslm-orchestration-poc/
├── README.md
├── requirements.txt
├── .env.example
├── .gitignore
│
├── app/
│   ├── __init__.py
│   ├── config.py
│   ├── errors.py
│   │
│   ├── core/
│   │   ├── __init__.py
│   │   ├── logging.py
│   │   └── database.py
│   │
│   ├── llm/
│   │   ├── __init__.py
│   │   ├── base.py
│   │   ├── openai.py
│   │   ├── anthropic.py
│   │   └── gemini.py
│   │
│   ├── rag/                    ✅ COMPLETED & SIMPLIFIED
│   │   ├── __init__.py         # Clean exports, no broken imports
│   │   ├── embeddings/         # Modular embedding implementations
│   │   │   ├── __init__.py
│   │   │   ├── base.py         # Abstract base class
│   │   │   ├── openai.py       # OpenAI embeddings
│   │   │   ├── huggingface.py  # HuggingFace embeddings
│   │   │   └── factory.py      # Factory with fallback support
│   │   ├── retriever/          # Retrieval components
│   │   │   ├── __init__.py
│   │   │   ├── retriever.py    # Base & Hybrid retrievers (simplified)
│   │   │   ├── context_window.py  # Context window management
│   │   │   └── query_rewriter.py  # Query enhancement
│   │   ├── knowledge_base.py   # Complete search implementation
│   │   ├── vector_store.py     # PostgreSQL/pgvector implementation
│   │   └── utils.py           # Configuration & initialization helpers
│   │
│   ├── orchestration/
│   │   ├── __init__.py
│   │   ├── state.py
│   │   ├── planner.py
│   │   ├── memory.py
│   │   ├── coordinator.py
│   │   └── graph.py
│   │
│   ├── agents/
│   │   ├── __init__.py
│   │   ├── base.py                  # Base agent interface
│   │   ├── co_ceo.py                # Planner & coordinator (has tools too)
│   │   ├── finance.py               # Finance agent — cost analysis, forecasting
│   │   └── marketing.py            # Marketing agent — campaign & competition insight
│   │
│   ├── tools/
│   │   ├── __init__.py
│   │   ├── base.py
│   │   ├── registry.py
│   │   └── web_search.py
│   │
│   └── tracing/
│       ├── __init__.py
│       ├── tracer.py
│       ├── visualizer.py
│       └── events.py
│
├── cli/
│   ├── __init__.py
│   ├── main.py
│   ├── test_rag.py
│   ├── test_orchestration.py
│   └── visualize.py
│
├── data/
│   ├── documents/
│   └── traces/
│
├── tests/
│   ├── __init__.py
│   ├── test_rag.py
│   ├── test_agents.py
│   ├── test_orchestration.py
│   └── test_tools.py
│
├── docs/
│   ├── architecture.md
│   ├── orchestration.md
│   └── api.md
│
└── scripts/
    ├── setup_db.py
    ├── load_documents.py
    └── run_tests.py
```

---

## 🗓️ 12-Day Implementation Timeline

### Days 1–2: Foundation Setup

**Day 1:**

* Create repo structure
* Migrate config, database, and LLM adapters
* Add basic logging & error handling
* Set up requirements and README

**Day 2:**

* Migrate RAG components
* Set up PostgreSQL + pgvector
* Create document loading scripts
* Test basic RAG pipeline

---

### Days 3–4: Orchestration Core

**Day 3:**

* Design `state.py`
* Implement `planner.py` (task decomposition)
* Create `memory.py`
* Define `coordinator.py`

**Day 4:**

* Build `graph.py` (dynamic graph)
* Implement tracing (`tracer.py`, `events.py`)
* Test core orchestration

---

### Days 5–6: Agent Implementation

**Day 5:**

* Build `BaseAgent`
* Implement `CoCEOAgent` with planning
* Add task decomposition
* Provide basic reasoning loop
* Enable direct RAG/WebSearch tool access from CoCEO

**Day 6:**

* Implement `FinanceAgent` and `MarketingAgent`
* Enable agent communication
* Integrate tool usage into all agents
* Validate multi-agent orchestration and fallback handling

---

### Days 7–8: Tool System & Web Search

**Day 7:**

* Create dynamic tool system
* Build `ToolRegistry`
* Implement `WebSearchTool`
* Integrate tool results into agent output

**Day 8:**

* Connect tools with orchestration
* Add usage logging
* Implement tool-specific error handling

---

### Days 9–10: CLI & Testing

**Day 9:**

* Build CLI interface
* Implement CLI testing hooks
* Add trace visualization in CLI
* Enable interactive test mode with multi-agent delegation inspection

**Day 10:**

* Implement comprehensive test suite
* Add full integration tests
* Build benchmarking tools
* Validate agent routing, tool usage, and trace logging end-to-end

---

### Days 11–12: Integration & Polish

**Day 11:**

* Run full-system tests
* Optimize performance
* Fix bugs
* Update documentation

**Day 12:**

* Final QA pass
* Create demo scenarios
* Polish CLI
* Prepare presentation

---

## 🔑 Key Implementation Principles

### 1. **Orchestration-First Design**

```python
class BaseAgent:
    def __init__(self, llm_adapter, tools=None):
        self.llm_adapter = llm_adapter
        self.tools = tools or []

    async def process_task(self, task, context):
        pass

    async def use_tool(self, task, context):
        for tool in self.tools:
            if tool.can_handle(task):
                return await tool.run(task, context)

class CoCEOAgent(BaseAgent):
    async def plan_execution(self, query):
        documents = await self.use_tool("search background", context={})
        # Use retrieved docs to create plan

    async def coordinate_agents(self, plan, agents):
        pass
```

### 2. **Dynamic Tool Selection**

```python
class ToolRegistry:
    def select_tools(self, task_type, context):
        pass

class WebSearchTool:
    def can_handle(self, task):
        pass
```

### 3. **Comprehensive Tracing**

```python
class OrchestrationTracer:
    def trace_planning(self, query, plan):
        pass

    def trace_agent_interaction(self, agent, task, result):
        pass

    def trace_tool_usage(self, tool, input, output):
        pass
```

📝 *Trace logs should include which agent handled which task, what tool was used (if any), and the results of each step — including direct tool usage by CoCEO.*

---

## 📦 Migration Strategy

### Week 1 (Days 1–7): Core Migration

* Extract reusable components (RAG, adapters, config)
* Refactor into simpler interfaces
* Build orchestration core from scratch

### Week 2 (Days 8–12): Integration & Testing

* Implement agents cleanly with planning capabilities
* Integrate tools dynamically and test multi-agent flow
* Build and test CLI end-to-end with trace replay

---

## ✅ Success Metrics (By Day 12)

* 3 working agents with orchestration
* Web search tool integrated
* RAG with pgvector working
* Task decomposition engine
* Orchestration tracing operational
* CLI test and visualization tools in place
* Full integration test suite
* CLI supports inspecting multi-agent execution paths

---

## 🚨 Risk Mitigation

### High-Risk Areas

* LangGraph complexity
* Agent coordination
* Tool-system flexibility

### Mitigation Tactics

* Daily checkpoints
* Incremental tests
* Simple working fallback designs
* Prioritize functionality over features

---

> This plan is aggressive but focused. Build small, working units. Avoid replication of old complexity. Traceability and orchestration clarity come first.
