[project]
name = "businesslm-python-orchestration-poc"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "alembic>=1.16.1",
    "pydantic>=2.11.5",
    "pydantic-settings>=2.9.1",
    "sqlalchemy>=2.0.41",
    "psycopg2-binary>=2.9.0",
    "pgvector>=0.2.0",
    "sentence-transformers>=4.1.0",
    "openai>=1.82.1",
    "anthropic>=0.52.1",
    "google-generativeai>=0.8.5",
]
