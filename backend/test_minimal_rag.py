#!/usr/bin/env python3
"""
Minimal RAG Test

Quick test to identify what's causing the RAG pipeline to hang.
"""

import sys
import asyncio
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent
sys.path.insert(0, str(backend_path))

print("🚀 Starting minimal RAG test...")

try:
    print("1. Testing configuration import...")
    from app.config import get_settings
    settings = get_settings()
    print(f"✅ Configuration loaded: {settings.DEFAULT_EMBEDDING_MODEL}")
    
    print("2. Testing database connection...")
    from app.core.db.database import check_database_connection
    if check_database_connection():
        print("✅ Database connection successful")
    else:
        print("❌ Database connection failed")
        sys.exit(1)
    
    print("3. Testing embedding factory import...")
    from rag.embeddings.factory import EmbeddingModelFactory
    print("✅ Embedding factory imported")
    
    print("4. Testing LLM factory import...")
    from llm.factory import LLMAdapterFactory
    print("✅ LLM factory imported")
    
    async def test_embedding():
        print("5. Testing embedding model creation...")
        try:
            embedding_model = EmbeddingModelFactory.create_model()
            print(f"✅ Embedding model created: {type(embedding_model).__name__}")
            
            print("6. Testing embedding generation...")
            test_text = "Hello world"
            embedding = await embedding_model.embed_text(test_text)
            print(f"✅ Embedding generated: {len(embedding)} dimensions")
            return True
        except Exception as e:
            print(f"❌ Embedding test failed: {e}")
            return False
    
    async def test_llm():
        print("7. Testing LLM adapter creation...")
        try:
            llm_adapter = LLMAdapterFactory.create_adapter()
            print(f"✅ LLM adapter created: {type(llm_adapter).__name__}")
            
            print("8. Testing LLM completion...")
            response = await llm_adapter.complete("What is 2+2?")
            print(f"✅ LLM response: {response[:50]}...")
            return True
        except Exception as e:
            print(f"❌ LLM test failed: {e}")
            return False
    
    async def main():
        embedding_ok = await test_embedding()
        llm_ok = await test_llm()
        
        if embedding_ok and llm_ok:
            print("🎉 All tests passed! RAG pipeline components are working.")
            return 0
        else:
            print("❌ Some tests failed.")
            return 1
    
    result = asyncio.run(main())
    sys.exit(result)
    
except Exception as e:
    print(f"❌ Test failed with error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
